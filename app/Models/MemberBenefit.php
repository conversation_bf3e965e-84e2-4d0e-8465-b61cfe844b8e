<?php

namespace App\Models;

use App\Traits\FamilyBenefitTrait;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Benefit;
use App\Models\BenefitOption;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;

class MemberBenefit extends Model
{
    use HasFactory;
    use FamilyBenefitTrait;

    protected $table = 'member_benefits';

    public $timestamps = false;
    protected $guarded = ["id"];

    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class, 'member_id', 'id')->select('id', 'name');
    }

    public function benefit(): BelongsTo
    {
        return $this->belongsTo(Benefit::class, 'benefit_id', 'id')->select('id', 'name');
    }

    public function benefitOption()
    {
        return $this->belongsTo(BenefitOption::class, 'benefit_option_id');
    }

    public function getOzelBenefit($member_id)
    {

        $results = DB::table('member_benefits')
            ->join('benefits', 'member_benefits.benefit_id', '=', 'benefits.id')
            ->join('benefit_options', 'member_benefits.benefit_option_id', '=', 'benefit_options.id')
            ->where('member_benefits.member_id', $member_id)
            ->where('benefits.name', 'like', '%özel sağlık%')
            ->select('member_benefits.*', 'benefit_options.*') // İhtiyacınıza göre seçilecek sütunlar
            ->get();

        return $results;
    }

    // public function getBenefits($memberId)
    // {
    //     if (!$memberId) {
    //         abort(400, 'No member_id provided for getBenefits'); // 400 Bad Request
    //     }

    //     return $this->where('member_benefits.member_id', $memberId)
    //         ->select([
    //             'benefits.*',
    //             'benefits.id AS benefit_id',
    //             'benefit_groups.id as group_id',
    //             'benefit_groups.name AS group_name',
    //             'benefit_groups.name_en AS group_name_en',
    //             'benefit_groups.description_en AS group_description_en',
    //             'benefit_groups.description AS group_description',
    //             'benefit_groups.image AS group_image',
    //             'benefit_groups.image_en AS group_image_en',
    //             'benefit_options.id as option_id',
    //             'benefit_options.name as option_name',
    //             'benefit_options.name_en as option_name_en',
    //             DB::raw('COALESCE(p.price, benefit_options.price) as option_price'),
    //             'member_benefits.amount',
    //             DB::raw('IF(
    //                 has_flexible_price,
    //                 IF(flexible_price = "Adet",
    //                     COALESCE(p.price, benefits.price) * member_benefits.amount,
    //                     member_benefits.amount
    //                 ),
    //                 COALESCE(p.price, benefit_options.price, benefits.price)
    //             ) AS real_price')
    //         ])
    //         ->leftJoin('benefit_options', 'benefit_options.id', '=', 'member_benefits.benefit_option_id')
    //         ->leftJoin('benefits', 'member_benefits.benefit_id', '=', 'benefits.id')
    //         ->leftJoin('benefit_groups', 'benefits.benefit_group_id', '=', 'benefit_groups.id')
    //         ->leftJoin('member_benefit_prefs as p', function ($join) use ($memberId) {
    //             $join->on('p.benefit_option_id', '=', 'benefit_options.id')
    //                 ->where('p.is_visible', '=', 1)
    //                 ->orWhere(function ($query) use ($memberId) {
    //                     $query->where('p.benefit_id', '=', 'member_benefits.benefit_id')
    //                         ->where('p.benefit_option_id', '=', 0);
    //                 });
    //             $join->where('member_benefits.member_id', '=', $memberId)
    //                 ->where('p.member_id', '=', $memberId);
    //         })
    //         ->orderBy('benefit_groups.order_no')
    //         ->get();
    //         // $memberBenefit=new MemberBenefit();
    //         $benefits = $this->getBenefits($memberId);
    //         $benefitsGrouped = $benefits->groupBy('benefit_id');


    //         return $benefitsGrouped;
    // }

    public function getBenefits($memberId)
    {
        if (!$memberId) {
            abort(400, 'No member_id provided for getBenefits'); // 400 Bad Request
        }

        return $this->where('member_benefits.member_id', $memberId)
            ->select([
                'benefits.*',
                'benefits.id AS benefit_id',
                'benefit_groups.id as group_id',
                'benefit_groups.name AS group_name',
                'benefit_groups.name_en AS group_name_en',
                'benefit_groups.description_en AS group_description_en',
                'benefit_groups.description AS group_description',
                'benefit_groups.image AS group_image',
                'benefit_groups.image_en AS group_image_en',
                'benefit_options.id as option_id',
                'benefit_options.name as option_name',
                'benefit_options.name_en as option_name_en',
                DB::raw('COALESCE(p.price, benefit_options.price) as option_price'),
                'member_benefits.amount',
                DB::raw('CASE
                    WHEN has_flexible_price = 1 THEN
                        CASE
                            WHEN flexible_price = "Adet" THEN COALESCE(p.price, benefits.price) * member_benefits.amount
                            ELSE member_benefits.amount
                        END
                    ELSE COALESCE(p.price, benefit_options.price, benefits.price)
                END AS real_price')
            ])
            ->leftJoin('benefit_options', 'benefit_options.id', '=', 'member_benefits.benefit_option_id')
            ->leftJoin('benefits', 'member_benefits.benefit_id', '=', 'benefits.id')
            ->leftJoin('benefit_groups', 'benefits.benefit_group_id', '=', 'benefit_groups.id')
            ->leftJoin(DB::raw('member_benefit_prefs p'), function ($join) use ($memberId) {
                $join->on(DB::raw('p.benefit_option_id'), '=', DB::raw('benefit_options.id'))
                    ->where(DB::raw('p.is_visible'), '=', 1)
                    ->orWhere(function ($query) use ($memberId) {
                        $query->where(DB::raw('p.benefit_id'), '=', DB::raw('member_benefits.benefit_id'))
                            ->where(DB::raw('p.benefit_option_id'), '=', 0);
                    });
                $join->where(DB::raw('member_benefits.member_id'), '=', $memberId)
                    ->where(DB::raw('p.member_id'), '=', $memberId);
            })
            ->orderBy('benefit_groups.order_no')
            ->get();
    }

    public function getBenefitsPDF($memberId)
{
    if (!$memberId) {
        abort(400, 'No member_id provided for getBenefits'); // 400 Bad Request
    }

    return $this->where('member_benefits.member_id', $memberId)
        ->whereNotNull('member_benefits.amount') // NULL olmayan 'amount' değerlerini seç
        ->where(function($query) {
            $query->where('member_benefits.benefit_option_id', '=', 0)
                  ->orWhere('member_benefits.amount', '>', 0);
        })
        ->select([
            'benefits.*',
            'benefits.id AS benefit_id',
            'benefit_groups.id as group_id',
            'benefit_groups.name AS group_name',
            'benefit_groups.name_en AS group_name_en',
            'benefit_groups.description_en AS group_description_en',
            'benefit_groups.description AS group_description',
            'benefit_groups.image AS group_image',
            'benefit_groups.image_en AS group_image_en',
            'benefit_options.id as option_id',
            'benefit_options.name as option_name',
            'benefit_options.name_en as option_name_en',
            DB::raw('COALESCE(p.price, benefit_options.price) as option_price'),
            'member_benefits.amount',
            DB::raw('CASE
                WHEN has_flexible_price = 1 THEN
                    CASE
                        WHEN flexible_price = "Adet" THEN COALESCE(p.price, benefits.price) * member_benefits.amount
                        ELSE member_benefits.amount
                    END
                ELSE COALESCE(p.price, benefit_options.price, benefits.price)
            END AS real_price')
        ])
        ->leftJoin('benefit_options', 'benefit_options.id', '=', 'member_benefits.benefit_option_id')
        ->leftJoin('benefits', 'member_benefits.benefit_id', '=', 'benefits.id')
        ->leftJoin('benefit_groups', 'benefits.benefit_group_id', '=', 'benefit_groups.id')
        ->leftJoin(DB::raw('member_benefit_prefs p'), function ($join) use ($memberId) {
            $join->on(DB::raw('p.benefit_option_id'), '=', DB::raw('benefit_options.id'))
                ->where(DB::raw('p.is_visible'), '=', 1)
                ->orWhere(function ($query) use ($memberId) {
                    $query->where(DB::raw('p.benefit_id'), '=', DB::raw('member_benefits.benefit_id'))
                        ->where(DB::raw('p.benefit_option_id'), '=', 0);
                });
            $join->where(DB::raw('member_benefits.member_id'), '=', $memberId)
                ->where(DB::raw('p.member_id'), '=', $memberId);
        })
        ->orderBy('benefit_groups.order_no')
        ->get();
}
    


    public function getFinalBenefits($member_id)
    {
        $memberBenefitsCart = new MemberBenefitCart();
        $memberBenefit = new MemberBenefit();

        $selectedBenefitsData = $memberBenefitsCart->getBenefits($member_id);
        $predefinedBenefits = $memberBenefit->getBenefits($member_id);

        $finalBenefits = $predefinedBenefits;
        foreach ($selectedBenefitsData as $key => $selectedBenefit) {
            $finalBenefits[$key] = $selectedBenefit;
        }

        return $finalBenefits;
    }

    public function getFamilyBenefits($memberId = false, $useSelection = false)
    {
        if (!$memberId) {
            $memberId = auth()->user()->id;
        }

        $benefits = $useSelection ? $this->getFinalBenefits($memberId) : $this->getBenefits($memberId);

        $memberFamilyModel = new MemberFamily();
        $children = $memberFamilyModel->getChildren($memberId);
        $partner = $memberFamilyModel->getPartner($memberId);

        $member = Member::find($memberId);
        $familyPercentage = $member->family_percentage ?: 0;

        $familyBenefits = [];

        foreach ($benefits as $benefit) {
            if ($partner) {
                $info = $this->getBenefitInfo($benefit['id'], $benefit['option_id'], null, 'partner');
                if (!empty($info)) {
                    $info['id'] = $partner->id;
                    $info['relative_name'] = $partner->name;
                    if ($info['has_company_support']) {
                        $info['real_price'] = $info['real_price'] * $familyPercentage / 100;
                    }
                    $familyBenefits[$benefit['id']][$partner->id] = $info;
                }
            }

            if ($children) {
                foreach ($children as $child) {
                    $childInfo = $this->getBenefitInfo($benefit['id'], $benefit['option_id'], $child->birth_date, 'child');
                    if (!empty($childInfo)) {
                        $childInfo['id'] = $child->id;
                        $childInfo['relative_name'] = $child->name;
                        if ($childInfo['has_company_support']) {
                            $childInfo['real_price'] = $childInfo['real_price'] * $familyPercentage / 100;
                        }
                        $familyBenefits[$benefit['id']][$child->id] = $childInfo;
                    }
                }
            }
        }

        return $familyBenefits;
    }

    public function getBenefitInfo($benefitId, $benefitOptionId, $birthDate, $type)
    {
        $age = $this->calculateAge($birthDate);

        $query = DB::table('family_benefits')
            ->join('family_benefit_details as fbd', 'fbd.family_benefit_id', '=', 'family_benefits.id')
            ->join('benefits', 'family_benefits.benefit_id', '=', 'benefits.id')
            ->leftJoin('benefit_options', 'benefit_options.id', '=', 'family_benefits.benefit_option_id')
            ->select('fbd.*', 'family_benefits.prerequisite_benefit_id', 'fbd.price AS real_price', 'fbd.id AS detail_id', 'benefits.*', 'benefits.benefit_group_id AS group_id', 'benefit_options.name AS option_name', 'family_benefits.has_company_support', 'family_benefits.benefit_option_id AS option_id')
            ->where('family_benefits.benefit_id', $benefitId)
            ->where('fbd.relation', $type)
            ->where('family_benefits.benefit_option_id', $benefitOptionId);

        if ($type === 'child') {
            $query->where('fbd.age_start', '<=', $age)
                ->where('fbd.age_end', '>=', $age);
        }

        return $query->first();
    }


    public function getIzinprice($benefit_id)
    {
        $data = Benefit::where('id', $benefit_id)->first();
        return $data;
    }


}
