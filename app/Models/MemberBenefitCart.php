<?php

namespace App\Models;

use App\Traits\FamilyBenefitTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Member;
use App\Models\Benefit;
use Illuminate\Support\Facades\Auth;

class MemberBenefitCart extends Model
{
    use HasFactory;
    use FamilyBenefitTrait;

    protected $table = 'member_benefits_cart';
    public $timestamps = false;
    protected $guarded = ["id"];

    // public static function getBenefits($member_id)
    // {
    //     if(!$member_id) {
    //         die('No member_id on getBenefits');
    //     }

    //     $data = Benefit::select(
    //         'benefits.*',
    //         'benefits.id AS benefit_id',
    //         'benefit_groups.id as group_id',
    //         'benefit_groups.name AS group_name',
    //         'benefit_options.id as option_id',
    //         'benefit_options.name as option_name',
    //         DB::raw('COALESCE(p.price,benefit_options.price) as option_price'),
    //         'member_benefits.amount',
    //         DB::raw('
    //             IF(
    //                 has_flexible_price,
    //                 IF(flexible_price = "Adet",
    //                    COALESCE(p.price, benefits.price) * member_benefits.amount,
    //                    member_benefits.amount
    //                 ),
    //                 COALESCE(p.price,benefit_options.price, benefits.price)
    //             ) AS real_price'
    //         )
    //     )
    //     ->leftJoin('member_benefits', 'benefits.id', '=', 'member_benefits.benefit_id')
    //     ->leftJoin('benefit_options', 'benefit_options.id', '=', 'member_benefits.benefit_option_id')
    //     ->leftJoin('benefit_groups', 'benefits.benefit_group_id', '=', 'benefit_groups.id')
    //     ->leftJoin('member_benefit_prefs as p', function($join) use ($member_id) {
    //         $join->on('p.benefit_option_id', '=', 'benefit_options.id')
    //              ->orWhere(function($query) use ($member_id) {
    //                  $query->on('p.benefit_id', '=', 'member_benefits.benefit_id')
    //                        ->where('p.benefit_option_id', 0);
    //              })
    //              ->where('member_benefits.member_id', $member_id)
    //              ->where('p.member_id', $member_id)
    //              ->where('p.is_visible', 1);
    //     })
    //     ->where('member_benefits.member_id', $member_id)
    //     ->orderBy('benefit_groups.order_no', 'ASC')
    //     ->get();

    //     return $data;
    // }


    public static function getBenefitsPDF($memberId = null)
    {
        if (!$memberId) {
            $memberId = Member::get('id');
        }

        $query = DB::table('member_benefits_cart')
            ->selectRaw("
            benefits.*,
            benefits.id AS benefit_id,
            benefit_groups.id as group_id,
            benefit_groups.`name` AS group_name,
            benefit_options.`id` as option_id,
            benefit_options.`name` as option_name,
            COALESCE(p.price, benefit_options.price) as option_price,
            member_benefits_cart.amount,
            CASE
                WHEN has_flexible_price = 1 THEN
                    CASE
                        WHEN flexible_price = 'Adet' THEN COALESCE(p.price, benefits.price) * member_benefits_cart.amount
                        ELSE member_benefits_cart.amount
                    END
                ELSE COALESCE(p.price, benefit_options.price, benefits.price)
            END AS real_price
        ")
            ->where('member_benefits_cart.member_id', $memberId)
            ->where('member_benefits_cart.member_family_id', 0)
            ->orderBy('benefit_groups.order_no');

        // Tabloları birleştir
        $query->leftJoin('benefit_options', 'benefit_options.id', '=', 'member_benefits_cart.benefit_option_id');
        $query->leftJoin('benefits', 'member_benefits_cart.benefit_id', '=', 'benefits.id');
        $query->leftJoin('benefit_groups', 'benefits.benefit_group_id', '=', 'benefit_groups.id');
        $query->leftJoin('member_benefit_prefs as p', function ($join) use ($memberId) {
            $join->on('p.benefit_option_id', '=', 'benefit_options.id')
                ->orWhere(function ($query) use ($memberId) {
                    $query->on('p.benefit_id', '=', 'member_benefits_cart.benefit_id')
                        ->where('p.benefit_option_id', 0);
                })
                ->where('p.member_id', $memberId)
                ->where('p.is_visible', 1);
        });

        // Sorguyu çalıştır ve sonuçları al
        $results = $query->get();

        // Sonuçları düz bir diziye dönüştür
        $benefitsArray = $results->mapWithKeys(function ($item) {
            if (is_object($item)) {
                // Eğer $item bir nesne ise, array olarak dönüştür
                return [$item->id => (array)$item];
            } else {
                // Eğer $item zaten bir array ise, olduğu gibi dönüştür
                return [$item['id'] => $item];
            }
        });

        return $benefitsArray;


    }

    public static function getBenefits($memberId = null)
    {
        if (!$memberId) {
            $memberId = Member::get('id');
        }

        $benefits = DB::table('member_benefits_cart')
            ->selectRaw("
            benefits.*,
            benefits.id AS benefit_id,
            benefit_groups.id as group_id,
            benefit_groups.`name` AS group_name,
            benefit_groups.`name_en` AS group_name_en,
            benefit_options.`id` as option_id,
            benefit_options.`name` as option_name,
            benefit_options.`name_en` as option_name_en,
            COALESCE(p.price, benefit_options.price) as option_price,
            member_benefits_cart.amount,
            CASE
                WHEN has_flexible_price = 1 THEN
                    CASE
                        WHEN flexible_price = 'Adet' THEN COALESCE(p.price, benefits.price) * member_benefits_cart.amount
                        ELSE member_benefits_cart.amount
                    END
                ELSE COALESCE(p.price, benefit_options.price, benefits.price)
            END AS real_price
        ")
            ->join('benefits', 'member_benefits_cart.benefit_id', '=', 'benefits.id')
            ->leftJoin('benefit_options', 'benefit_options.id', '=', 'member_benefits_cart.benefit_option_id')
            ->leftJoin('benefit_groups', 'benefits.benefit_group_id', '=', 'benefit_groups.id')
            ->leftJoin('member_benefit_prefs as p', function ($join) use ($memberId) {
                $join->on('p.benefit_option_id', '=', 'benefit_options.id')
                    ->orWhere(function ($query) use ($memberId) {
                        $query->on('p.benefit_id', '=', 'member_benefits_cart.benefit_id')
                            ->where('p.benefit_option_id', 0);
                    })
                    ->where('p.member_id', $memberId)
                    ->where('p.is_visible', 1);
            })
            ->where('member_benefits_cart.member_id', $memberId)
            ->where('member_benefits_cart.member_family_id', 0)
            ->orderBy('benefit_groups.order_no')
            ->get();

        $benefitsGrouped = $benefits->groupBy('benefit_id');

        return $benefitsGrouped;

    }

    public function addBenefitsByOptionId($option_id, $member_id = null)
    {

        $member = Member::where('id', Auth::guard('member')->id())->first();

        if (!$member_id) {
            $member_id = $member->id;
        }

        $checkResult = DB::table('member_benefits_cart')
            ->where('member_id', $member_id)
            ->where('benefit_option_id', $option_id)
            ->where('member_family_id', 0)
            ->count();

        $result = [];

        if ($checkResult) {
            $result['success'] = false;
            $result['message'] = 'Bu hak zaten mevcut. Yeniden eklenemez.';
        } else {
            $option_detail = $this->getBenefitOptionsDetailById($option_id);

            $old_option = DB::table('member_benefits_cart')
                ->where('member_id', $member_id)
                ->where('benefit_id', $option_detail->benefit_id)
                ->where('member_family_id', 0)
                ->first();

            if (!empty($old_option)) {
                $old_option_id = $old_option->benefit_option_id;

                $family_benefit_info = DB::table('family_benefits')
                    ->where('benefit_option_id', $old_option_id)
                    ->where('benefit_id', $option_detail->benefit_id)
                    ->first();

                if (!empty($family_benefit_info)) {
                    DB::table('member_benefits_cart')
                        ->where('member_id', $member_id)
                        ->where('benefit_id', $option_detail->benefit_id)
                        ->delete();
                }
            }

            DB::table('member_benefits_cart')
                ->where('member_id', $member_id)
                ->where('benefit_id', $option_detail->benefit_id)
                ->where('member_family_id', 0)
                ->delete();

            $benefitModel = new Benefit();
            $data = [];
            $data['benefit_id'] = $option_detail->benefit_id;
            $data['amount'] = $benefitModel->getCheckBenefitOptionPref($option_id, $member_id, $option_detail->price);
            $data['member_id'] = $member_id;
            $data['update_status'] = 1;
            $data['benefit_option_id'] = $option_id;
            $data['member_family_id'] = 0;

            if (DB::table('member_benefits_cart')->insert($data)) {
                $result['success'] = true;
                $result['message'] = 'Hak eklendi.';
                $result['benefit_id'] = $data['benefit_id'];
            } else {
                Log::error('Hak eklenirken hata olustu. benefit op:' . $option_id);
                $result['success'] = false;
                $result['message'] = 'Bir hata oluştu. Hak Eklenemedi.';
                $result['benefit_id'] = $data['benefit_id'];
            }
        }

        return $result;
    }

    public function getBenefitOptionsDetailById($option_id)
    {
        return DB::table('benefit_options')->where('id', $option_id)->first();
    }

    public function getCheckBenefitOptionPref($option_id, $member_id, $price)
    {
        if ($option_id) {
            $select = DB::table('member_benefit_prefs')
                ->where('benefit_option_id', $option_id)
                ->where('member_id', $member_id)
                ->where('is_visible', 1)
                ->first();

            if ($select) {
                return $select->price;
            } else {
                return $price;
            }
        } else {
            return $price;
        }
    }

    public function addBenefitsByBenefitId($benefitId, $amount, $append = false)
    {
        // Benefiti detayını al
        $benefitDetail = Benefit::find($benefitId);

        if (!$benefitDetail) {
            Log::error('Belirtilen benefit_id ile bir fayda bulunamadı: ' . $benefitId);
            return ['success' => false, 'message' => 'Hak eklenirken bir hata oluştu.'];
        }


        $member = Member::where('id', Auth::guard('member')->id())->first();

        if ($member) {
            $memberId = $member->id;
        }

        $data = [
            'benefit_id' => $benefitId,
            'member_id' => $memberId,
            'member_family_id' => 0, // Varsayılan değeri 0 olarak ayarladım.
            'update_status' => 1,
            'amount' => $amount
        ];

        // Eğer append true ise, mevcut miktarı arttır
        if ($append) {
            $existingBenefit = DB::table('member_benefits_cart')
                ->where('member_id', $memberId)
                ->where('member_family_id', 0)
                ->where('benefit_id', $benefitId)
                ->first();

            if ($existingBenefit) {
                $data['amount'] += $existingBenefit->amount;
            }
        }

        try {
            // Kaydı veritabanına ekle veya güncelle
            DB::table('member_benefits_cart')->updateOrInsert([
                'member_id' => $memberId,
                'member_family_id' => 0,
                'benefit_id' => $benefitId
            ], $data);

            return ['success' => true, 'message' => 'Hak Eklendi.'];

        } catch (\Exception $e) {
            Log::error('Hak eklenirken bir hata oluştu: ' . $e->getMessage());
            return ['success' => false, 'message' => 'Hak eklenirken bir hata oluştu.'];
        }
    }

    public function getFamilyBenefits($memberId = false, $noFamilyOption = false)
    {
        if (!$memberId) {
            $memberId = auth()->user()->id;
        }

        $memberFamilyModel = new MemberFamily();
        $partner = $memberFamilyModel->getPartner($memberId);
        $relatives = $memberFamilyModel->getChildren($memberId);

        if (!empty($partner)) {
            $relatives[] = $partner;
        }

        if (empty($relatives)) {
            return [];
        }

        $relativeIds = collect($relatives)->pluck('id');

        $member = Member::find($memberId);
        $familyPercentage = $member->family_percentage ?: 0;

        // Real Balance hesabı için, noFamilyOption true olduğunda, her zaman tam fiyat döndürüyoruz.
        $familyPrice = $noFamilyOption ? 'family_benefit_details.price' : DB::raw("CASE WHEN member_benefits_cart.family_option = 1 THEN family_benefit_details.price * {$familyPercentage} / 100 ELSE family_benefit_details.price END as real_price");

        $familyBenefits = $this->select('member_benefits_cart.*', 'member_family.name AS relative_name', 'family_benefits.benefit_option_id AS option_id',
            'benefits.benefit_group_id AS group_id', DB::raw($familyPrice), 'benefits.*', 'family_benefits.prerequisite_benefit_id')
            ->join('member_family', 'member_family.id', '=', 'member_family_id')
            ->leftJoin('family_benefit_details', 'family_benefit_details.id', '=', 'member_benefits_cart.family_benefit_detail_id')
            ->leftJoin('family_benefits', 'family_benefits.id', '=', 'family_benefit_details.family_benefit_id')
            ->leftJoin('benefit_options', 'benefit_options.id', '=', 'member_benefits_cart.benefit_option_id')
            ->leftJoin('benefits', 'member_benefits_cart.benefit_id', '=', 'benefits.id')
            ->leftJoin('benefit_groups', 'benefits.benefit_group_id', '=', 'benefit_groups.id')
            ->whereIn('member_family_id', $relativeIds)
            ->where('member_benefits_cart.member_id', $memberId)
            ->get()
            ->groupBy('benefit_id')
            ->map(function ($group) {
                return $group->keyBy('member_family_id');
            });

        return $familyBenefits;
    }

    public static function clear($member_id)
    {
        return self::where('member_id', $member_id)->delete();
    }

}
