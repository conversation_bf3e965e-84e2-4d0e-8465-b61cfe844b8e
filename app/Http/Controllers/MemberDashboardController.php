<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Slider;
use App\Models\BenefitGroup;
use App\Models\Benefit;
use App\Models\Member;
use App\Models\MemberBenefit;
use App\Models\FamilyBenefit;
use App\Models\Question;
use App\Models\MemberBenefitCart;
use App\Models\WelcomeMessage;
use Illuminate\Support\Facades\Redirect;
use App\Models\Announcement;

class MemberDashboardController extends Controller
{


    public function show()
    {


        //SLIDERS
        $sliders = Slider::all();
        //SLIDERS

        //WELCOME MESSAGES
        $welcome_messages = WelcomeMessage::orderBy('order_no', 'asc')->get();
        //WELCOME MESSAGES


        $member = Member::where('id', Auth::guard('member')->id())->first();

        if ($member) {
            $member_id = $member->id;
            $user_info = $member->toArray();
        }

        if ($user_info['is_approved'] == 1) {
            return redirect()->route('approval');
        }

        $m_benefit = new Benefit();
        $benefits = $m_benefit->getAll($member_id);


        $memberBenefit = new MemberBenefit();
        $predefinedBenefits = $memberBenefit->getBenefits($member_id);


        $ozelSaglik = $memberBenefit->getOzelBenefit($member_id);


        // $temel_haklar = [];
        //     $predefinedFlexiblePriceValues = [];
        //     foreach($predefinedBenefits as $predefinedBenefit)
        //     {
        //     if($predefinedBenefit['group_id'] == '1')
        //     {
        //         $temel_haklar[] = $predefinedBenefit;
        //     }

        //     if($predefinedBenefit['has_flexible_price'])
        //     {
        //         $predefinedFlexiblePriceValues[$predefinedBenefit['benefit_id']] = $predefinedBenefit['amount'];
        //     }
        // }

        $groupOneGeneralInfo = $predefinedBenefits->first(function ($benefit) {
            return $benefit['group_id'] == '1';
        });

        // Grup '1' için temel hakları ve esnek fiyatları toplayalım.
        $temelHaklar = [];
        $predefinedFlexiblePriceValues = [];
        foreach ($predefinedBenefits as $predefinedBenefit) {
            if ($predefinedBenefit['group_id'] == '1') {
                $temelHaklar[] = $predefinedBenefit->toArray(); // Eloquent modelini diziye çevir
            }

            if ($predefinedBenefit['has_flexible_price']) {
                $predefinedFlexiblePriceValues[$predefinedBenefit['benefit_id']] = $predefinedBenefit['amount'];
            }
        }


        // $benefits dizisini güncelleyelim.
        $benefits['1'] = [
            'id' => $groupOneGeneralInfo->group_id ?? 1,
            'order_no' => $groupOneGeneralInfo->order_no ?? 1,
            'name' => $groupOneGeneralInfo->group_name ?? 'Değiştirilemez Yan Haklar',
            'name_en' => $groupOneGeneralInfo->group_name_en ?? 'Core Benefits',
            // 'name' => $groupOneGeneralInfo->group_name ?? 'Temel Haklar Grubu',
            // 'name_en' => $groupOneGeneralInfo->group_name_en ?? 'Temel Haklar Grubu',
            'description' => $groupOneGeneralInfo->group_description ?? 'Grup açıklaması',
            'description_en' => $groupOneGeneralInfo->group_description_en ?? 'Group Explanation',
            'status' => $groupOneGeneralInfo->status ?? 2,
            'group_image' => $groupOneGeneralInfo->group_image ?? 'default-image.png',
            'group_image_en' => $groupOneGeneralInfo->group_image_en ?? 'default-image.png',
            'image' => $groupOneGeneralInfo->image ?? 'default-image.png',
            'image_en' => $groupOneGeneralInfo->image_en ?? 'default-image.png',
            'benefits' => $temelHaklar,

        ];


        $finalBenefits = $m_benefit->getFinalBenefits($member_id);


        $allFamilyBenefits = $m_benefit->getFamilyBenefits($member_id);

        $familyBenefit = new FamilyBenefit();
        $finalFamilyBenefits = $familyBenefit->getFinalFamilyBenefits($member_id);

        $selecteds = [];
        $values = [];


        foreach ($finalBenefits as $benefit) {
            $selecteds[$benefit->benefit_id] = $benefit->option_id ? $benefit->option_id : 1;

            if ($benefit->has_flexible_price) {
                $values[$benefit->benefit_id] = $benefit->amount;
            }
        }


        // foreach($benefits as $group_id => $benefit_group)
        // {
        //     if($benefit_group['benefits']) {
        //         foreach($benefit_group['benefits'] as $key => $benefit) {
        //             if($benefit['hide_if_not_predefined'] && ! $predefinedBenefits[$benefit['id']])
        //             {
        //                 unset($benefits[$group_id]['benefits'][$key]);
        //             }
        //         }
        //     } else {
        //         unset($benefits[$group_id]);
        //     }
        // }

        //icinde group id aradıgı yer aslında benim array keyim oldugu icin kod revize edildi.

        foreach ($benefits as $group_id => &$group) {

            // Eğer 'benefits' anahtarı varsa ve bir dizi ise işlemleri yap
            if (isset($group['benefits']) && is_array($group['benefits'])) {

                foreach ($group['benefits'] as $key => $benefit) {

                    // Eğer benefit 'hide_if_not_predefined' true ve predefinedBenefits içinde yoksa
                    if ($benefit['hide_if_not_predefined'] && !isset($predefinedBenefits[$key])) {
                        unset($group['benefits'][$key]); // Bu benefit'i sil
                    }
                }

                // Eğer bu grupta hiç benefit kalmadıysa, bu grubu da sil
                if (empty($group['benefits'])) {
                    unset($benefits[$group_id]);
                } else {
                    $group['benefits'] = array_values($group['benefits']); // Anahtarları sıralı hale getir
                }
            }
        }

        unset($group); // Referansı temizleme


        $predefinedBenefitIds = $predefinedBenefits->pluck('id')->all();


// $benefits dizisindeki her bir benefit grubunu dön
        foreach ($benefits as $group_id => $benefit_group) {
            if (isset($benefit_group['benefits'])) {  // Eğer bu grup içinde benefits varsa
                foreach ($benefit_group['benefits'] as $key => $benefit) {
                    // Eğer bu benefit 'predefined' olarak işaretlenmişse ve predefinedBenefits içinde yer almıyorsa
                    if ($benefit['hide_if_not_predefined'] && !in_array($benefit['id'], $predefinedBenefitIds)) {
                        // Bu benefiti $benefits dizisinden sil
                        unset($benefits[$group_id]['benefits'][$key]);
                    }
                }
                // İndeksleri yeniden sırala
                $benefits[$group_id]['benefits'] = array_values($benefits[$group_id]['benefits']);
            }
        }


        foreach ($allFamilyBenefits as $benefit_id => $familyBenefit) {
            foreach ($familyBenefit as $member_family_id => $memberFamilyBenefit) {
                if ($memberFamilyBenefit->prerequisite_benefit_id) {
                    if (!isset($finalFamilyBenefits[$memberFamilyBenefit->prerequisite_benefit_id][$member_family_id]) || $finalFamilyBenefits[$memberFamilyBenefit->prerequisite_benefit_id][$member_family_id]->real_price == '0.00') {
                        unset($allFamilyBenefits[$benefit_id][$member_family_id]);
                    }
                }
            }
        }


        //Data gelisi temizlendi ve daha sade hale getirildi.
        $opened = request()->input('opened') ? explode(',', request()->input('opened')) : [];

        // //PositiveBalance Controllerdaki ile aynı işlem
        // $mBenefit = new Benefit();
        // $memberBalance = $mBenefit->getMemberBalance($member_id);


        $paymentController = new PaymentController();
        $paymentData = $paymentController->init();


        // ksort($benefits);


        foreach ($benefits as $key => $value) {
            if (empty($value['benefits'])) {

                unset($benefits[$key]);
            }

        }


        //VIP OPTION ICIN GECICI COZUM
        if (!array_key_exists(18, $benefits)) {
            if (array_key_exists(17, $benefits)) {
                $newOptions = [];
                foreach ($benefits[17]['benefits'][0]['options'] as $option) {
                    if (strpos(strtoupper($option['name']), 'VIP') === false) {
                        $newOptions[] = $option;
                    }
                }
                $benefits[17]['benefits'][0]['options'] = $newOptions;
            }
        }
        //VIP OPTION ICIN GECICI COZUM


        $memberDashboardData = [
            'sliders' => $sliders,
            'welcome_messages' => $welcome_messages,
            'predefined_benefits' => $predefinedBenefits,
            'predefinedFlexiblePriceValues' => $predefinedFlexiblePriceValues,
            'final_family_benefits' => $finalFamilyBenefits,
            'opened' => $opened,
            'family_benefits' => $allFamilyBenefits,
            'selecteds' => $selecteds,
            'benefits' => $benefits,
            'values' => $values,
            'ozelSaglik' => $ozelSaglik
        ];


        $data = array_merge($memberDashboardData, $paymentData);


        return view('member_dashboard', $data);

    }


    public function addAction(Request $request)
    {
        // Optimize: Auth kullanıcısını direkt al, ekstra sorgu yapma
        $member_id = Auth::guard('member')->id();

        $optionId = $request->input('option_id');
        $append = $request->has('append');
        $izin = $request->has('izin');

        // Optimize: Tek instance oluştur ve yeniden kullan
        $benefitsCart = new MemberBenefitCart();

        if (!$optionId) {
            $benefitId = $request->input('benefit_id');

            // Optimize: Tekrarlanan kod kaldırıldı
            $amount = $this->calculateAmount($request);

            // Optimize: Static method kullan, instance oluşturma
            $checkLimits = Benefit::checkLimits($benefitId);

            // Optimize: Limit kontrolünü ayrı metoda taşı
            $limitValidation = $this->validateLimits($checkLimits, $amount);
            if ($limitValidation) {
                return $limitValidation;
            }

            $result = $benefitsCart->addBenefitsByBenefitId($benefitId, $amount, $append);
        } else {
            $result = $benefitsCart->addBenefitsByOptionId($optionId);
            $benefitId = $result['benefit_id'];
        }

        $announcement = new Announcement();
        $announcementId = $announcement->getByBenefitId($benefitId);


        return response()->json([
            'message' => $result['message'],
            'success' => $result['success'],
            'data' => ['announcement_id' => $announcementId]
        ]);
    }

    public function resetCart(Request $request)
    {
        $member = Member::where('id', Auth::guard('member')->id())->first();
        if ($member) {
            $member_id = $member->id;
        }

        MemberBenefitCart::clear($member_id);

        return redirect()->back()->with('success', 'Başarıyla temizlenmiştir.');
    }

    /**
     * Optimize: Amount hesaplama mantığını ayrı metoda taşı
     */
    private function calculateAmount(Request $request): float
    {
        $amount = $request->input('birey_field_p_adet', $request->input('birey_field'));
        $amount = str_replace(',', '.', $amount);
        return max(0, (float) $amount);
    }

    /**
     * Optimize: Limit validasyon mantığını ayrı metoda taşı
     */
    private function validateLimits(?array $checkLimits, float $amount): ?\Illuminate\Http\JsonResponse
    {
        if (!$checkLimits || $amount == 0) {
            return null;
        }

        // Min limit kontrolü
        if (!empty($checkLimits['min_amount']) &&
            $amount < $checkLimits['min_amount'] &&
            sprintf('%d', $checkLimits['min_amount']) != 0) {

            return response()->json([
                'message' => 'Seçtiğiniz hakta MİNİMUM alabileceğiniz toplam tutar ' . sprintf('%d', $checkLimits['min_amount']) . ' TL\'dir. Lütfen bu tutarın üzerinde bir değer giriniz.',
                'message_en' => 'The MINIMUM total amount you can select for this right is ' . sprintf('%d', $checkLimits['min_amount']) . ' TL. Please enter a value above this amount.',
                'success' => false,
                'data' => []
            ]);
        }

        // Max limit kontrolü
        if (!empty($checkLimits['max_amount']) &&
            $amount > $checkLimits['max_amount'] &&
            sprintf('%d', $checkLimits['max_amount']) != 0) {

            return response()->json([
                'message' => 'Seçtiğiniz hakta MAKSİMUM alabileceğiniz toplam tutar ' . sprintf('%d', $checkLimits['max_amount']) . ' TL\'dir. Lütfen bu tutarın altında bir değer giriniz.',
                'message_en' => 'The MAXIMUM total amount you can select for this right is ' . sprintf('%d', $checkLimits['max_amount']) . ' TL. Please enter a value below this amount.',
                'success' => false,
                'data' => []
            ]);
        }

        return null;
    }
}
