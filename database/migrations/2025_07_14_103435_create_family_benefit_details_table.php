<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('family_benefit_details', function (Blueprint $table) {
            $table->id();
            $table->string('relation')->nullable(); // 'spouse', 'child', etc.
            $table->float('age_start', 11, 2)->nullable();
            $table->float('age_end', 11, 2)->nullable();
            $table->float('price', 11, 2)->nullable();
            $table->integer('family_benefit_id');
            $table->boolean('update_status');
            $table->float('price_original', 11, 2)->nullable();
            // Model'de timestamps = false olduğu için timestamps eklemeyelim
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('family_benefit_details');
    }
};
