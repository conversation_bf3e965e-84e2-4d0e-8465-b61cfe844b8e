<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('member_benefits', function (Blueprint $table) {
            $table->id();
            $table->integer('benefit_id')->nullable();
            $table->float('amount', 11, 2)->nullable();
            $table->integer('member_id');
            $table->boolean('update_status');
            $table->integer('benefit_option_id')->nullable();
            // Model'de timestamps = false olduğu için timestamps eklemeyelim
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('member_benefits');
    }
};
