<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sliders', function (Blueprint $table) {
            $table->id();
            $table->float('order_no', 11, 6)->nullable();
            $table->string('link')->nullable();
            $table->string('name')->nullable();
            $table->string('image')->nullable();
            $table->string('link_en')->nullable();
            $table->string('name_en')->nullable();
            $table->string('image_en')->nullable();
            // Model'de timestamps = false olduğu için timestamps eklemeyelim
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sliders');
    }
};
