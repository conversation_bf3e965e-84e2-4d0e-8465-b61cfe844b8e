<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('benefit_options', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->text('description')->nullable();
            $table->text('alert_message')->nullable();
            $table->string('image')->nullable();
            $table->text('comment')->nullable();
            $table->string('alert_color')->nullable();
            $table->float('price', 11, 2)->nullable();
            $table->integer('status')->nullable();
            $table->integer('benefit_id');
            $table->boolean('update_status');
            $table->integer('order_no');
            $table->boolean('is_addon')->nullable();
            $table->float('price_original', 11, 2)->nullable();
            $table->boolean('has_details')->nullable();
            $table->text('details')->nullable();
            // İngilizce alanlar
            $table->string('name_en')->nullable();
            $table->text('description_en')->nullable();
            $table->text('alert_message_en')->nullable();
            $table->string('image_en')->nullable();
            $table->text('comment_en')->nullable();
            $table->text('details_en')->nullable();
            // Model'de timestamps = false olduğu için timestamps eklemeyelim
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('benefit_options');
    }
};
