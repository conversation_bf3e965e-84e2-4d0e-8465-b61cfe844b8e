<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('family_benefits', function (Blueprint $table) {
            $table->id();
            $table->integer('benefit_id')->nullable();
            $table->integer('benefit_option_id')->nullable();
            $table->integer('benefit_group_id')->nullable();
            $table->integer('prerequisite_benefit_id')->nullable();
            $table->boolean('has_company_support')->nullable();
            // Model'de timestamps = false olduğu için timestamps eklemeyelim
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('family_benefits');
    }
};
