<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('benefit_groups', function (Blueprint $table) {
            $table->id();
            $table->float('order_no', 11, 6);
            $table->string('name')->nullable();
            $table->text('description')->nullable();
            $table->integer('status')->nullable();
            $table->string('image')->nullable();
            $table->boolean('can_be_negative')->nullable();
            $table->integer('announcement_id')->nullable();
            $table->string('name_en')->nullable();
            $table->text('description_en')->nullable();
            $table->string('image_en')->nullable();
            // Model'de timestamps = false olduğu için timestamps eklemeyelim
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('benefit_groups');
    }
};
