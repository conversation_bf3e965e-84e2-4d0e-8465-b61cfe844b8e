<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('benefits', function (Blueprint $table) {
            $table->id();
            $table->float('order_no', 11, 6);
            $table->string('name')->nullable();
            $table->integer('benefit_group_id')->nullable();
            $table->text('description')->nullable();
            $table->text('alert_message')->nullable();
            $table->string('image')->nullable();
            $table->text('comment')->nullable();
            $table->string('alert_color')->nullable();
            $table->float('price', 11, 2)->nullable();
            $table->integer('status')->nullable();
            $table->boolean('has_choice')->nullable();
            $table->boolean('has_flexible_price')->nullable();
            $table->string('flexible_price')->nullable();
            $table->string('type')->nullable();
            $table->string('flexible_amount_title')->nullable();
            $table->string('title')->nullable();
            $table->boolean('hide_if_not_predefined')->nullable();
            $table->boolean('show_on_positive_balance')->nullable();
            $table->float('price_original', 11, 2)->nullable();
            $table->text('details')->nullable();
            $table->boolean('has_details')->nullable();
            $table->boolean('show_option_info')->nullable();
            $table->boolean('convert_price')->nullable();
            $table->boolean('equal_price')->nullable();
            $table->boolean('really_equal_price')->nullable();
            $table->float('min_amount', 11, 2)->nullable();
            $table->float('max_amount', 11, 2)->nullable();
            // İngilizce alanlar
            $table->string('name_en')->nullable();
            $table->text('description_en')->nullable();
            $table->text('alert_message_en')->nullable();
            $table->string('image_en')->nullable();
            $table->text('comment_en')->nullable();
            $table->string('flexible_amount_title_en')->nullable();
            $table->string('title_en')->nullable();
            $table->text('details_en')->nullable();
            // Eksik alanlar (SQL'de var ama yukarıda eksik)
            $table->string('desc_image')->nullable();
            $table->string('desc_image_en')->nullable();
            $table->string('alert_bg_color')->nullable();
            $table->string('alert_bg_color_en')->nullable();
            $table->string('alert_color_en')->nullable();
            // Model'de timestamps = false olduğu için timestamps eklemeyelim
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('benefits');
    }
};
