<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('members', function (Blueprint $table) {
            $table->id();
            $table->integer('item_id')->default(0);
            $table->string('seo')->nullable();
            $table->string('password')->nullable();
            $table->string('email')->nullable();
            $table->string('name')->nullable();
            $table->date('birth_date')->nullable();
            $table->integer('status')->nullable();
            $table->integer('join_date')->nullable();
            $table->string('password_temp')->nullable();
            $table->integer('lockout_at')->nullable();
            $table->integer('error_count')->nullable();
            $table->integer('password_temp_expire_date')->nullable();
            $table->integer('percentage')->nullable();
            $table->integer('firm_id')->nullable();
            $table->integer('department_id')->nullable();
            $table->integer('title_id')->nullable();
            $table->integer('position_id')->nullable();
            $table->string('identity_number')->nullable();
            $table->integer('workplace_id')->nullable();
            $table->boolean('is_approved')->nullable();
            $table->integer('approval_date')->nullable();
            $table->datetime('last_login_date')->nullable();
            $table->integer('family_percentage')->nullable();
            $table->string('maritial_status')->nullable();
            $table->integer('positive_balance')->default(0);
            // Model'de timestamps = false olduğu için timestamps eklemeyelim
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('members');
    }
};
