<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('static_pages', function (Blueprint $table) {
            $table->id();
            $table->string('seo')->nullable();
            $table->integer('item_id')->default(0);
            $table->string('name')->nullable();
            $table->string('title')->nullable();
            $table->text('content')->nullable();
            $table->string('name_en')->nullable();
            $table->string('title_en')->nullable();
            $table->text('content_en')->nullable();
            $table->boolean('before_login')->default(false);
            $table->boolean('is_visible')->default(true);
            // Model'de timestamps = false olduğu için timestamps eklemeyelim
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('static_pages');
    }
};
