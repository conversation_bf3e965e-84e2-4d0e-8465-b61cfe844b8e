<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('member_family', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable();
            $table->string('identity_number')->nullable();
            $table->integer('member_id');
            $table->boolean('update_status');
            $table->integer('order_no');
            $table->string('type')->nullable(); // 'child', 'spouse', etc.
            $table->date('birth_date')->nullable();
            // Model'de timestamps = false olduğu için timestamps eklemeyelim
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('member_family');
    }
};
