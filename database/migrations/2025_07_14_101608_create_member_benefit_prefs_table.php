<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('member_benefit_prefs', function (Blueprint $table) {
            $table->id();
            $table->integer('member_id')->nullable();
            $table->integer('benefit_option_id')->nullable();
            $table->string('price')->nullable();
            $table->string('is_visible')->nullable();
            $table->integer('benefit_id')->nullable();
            // Model'de timestamps = false olduğu için timestamps eklemeyelim
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('member_benefit_prefs');
    }
};
