<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BenefitGroupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        \App\Models\BenefitGroup::create([
            'id' => 1,
            'order_no' => 1.0,
            'name' => 'DEĞİŞTİRİLEMEZ (TEMEL) YAN HAKLARIN',
            'description' => '<p>Te<PERSON> Yan Haklar, ilgili hakkın içeriğinin uygun olmaması ve/veya yenileme dönemi dışında olunmasından dolayı vazgeçilemez ve/veya değiştirilemez.</p>',
            'status' => 2,
            'image' => '21-10/26/temel-yan-haklar.png',
            'can_be_negative' => 0,
            'announcement_id' => 0,
        ]);

        \App\Models\BenefitGroup::create([
            'id' => 11,
            'order_no' => 4.0,
            'name' => 'ESNETEBİLECEĞİN YAN HAKLARIN',
            'description' => '',
            'status' => 2,
            'image' => '19-04/24/hadiyet_ceki.png',
            'can_be_negative' => 0,
            'announcement_id' => 0,
        ]);

        \App\Models\BenefitGroup::create([
            'id' => 14,
            'order_no' => 2.0,
            'name' => 'ESNETEBİLECEĞİN YAN HAKLARIN',
            'description' => '',
            'status' => 2,
            'image' => '19-05/03/esnetilebilir-haklar.png',
            'can_be_negative' => 0,
            'announcement_id' => 0,
        ]);

        \App\Models\BenefitGroup::create([
            'id' => 15,
            'order_no' => 5.0,
            'name' => 'YAN HAK MENÜNE EKLEYEBİLECEĞİN ALINABİLİR FIRSATLAR',
            'description' => '',
            'status' => 2,
            'image' => '19-05/03/alinabilir-firsatlar.png',
            'can_be_negative' => 0,
            'announcement_id' => 0,
        ]);

        \App\Models\BenefitGroup::create([
            'id' => 16,
            'order_no' => 3.0,
            'name' => 'VIP EŞ - ÇOCUK SAĞLIK SİGORTASI',
            'description' => '',
            'status' => 2,
            'image' => '19-05/01/saglik-sigortasi_gorsel_png_648928.png',
            'can_be_negative' => 0,
            'announcement_id' => 0,
        ]);
    }
}
