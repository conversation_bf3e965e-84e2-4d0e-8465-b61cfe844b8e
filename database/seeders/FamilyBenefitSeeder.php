<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class FamilyBenefitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Dump dosyasından alınan family_benefits verisi
        $familyBenefit = \App\Models\FamilyBenefit::create([
            'id' => 1,
            'benefit_id' => 150, // Bu benefit'i daha sonra oluşturmamız gerekebilir
            'benefit_option_id' => 0,
            'benefit_group_id' => 16,
            'prerequisite_benefit_id' => 0,
            'has_company_support' => 1,
        ]);

        // Örnek family_benefit_details (şimdilik boş, gerekirse daha sonra eklenebilir)
        // \App\Models\FamilyBenefitDetail::create([
        //     'relation' => 'spouse',
        //     'age_start' => 18.00,
        //     'age_end' => 65.00,
        //     'price' => 1000.00,
        //     'family_benefit_id' => $familyBenefit->id,
        //     'update_status' => 1,
        //     'price_original' => 1000.00,
        // ]);

        // \App\Models\FamilyBenefitDetail::create([
        //     'relation' => 'child',
        //     'age_start' => 0.00,
        //     'age_end' => 18.00,
        //     'price' => 500.00,
        //     'family_benefit_id' => $familyBenefit->id,
        //     'update_status' => 1,
        //     'price_original' => 500.00,
        // ]);
    }
}
