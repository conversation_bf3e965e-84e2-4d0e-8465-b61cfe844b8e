<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SiteMovementSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        \App\Models\SiteMovement::create([
            'id' => 1,
            'name' => 'Site Kapatma',
            'extra1' => '0', // Site açık (0) / kapalı (1)
            'extra2' => null,
            'image' => null,
        ]);

        \App\Models\SiteMovement::create([
            'id' => 2,
            'name' => 'Mail Ayarları',
            'extra1' => '<EMAIL>', // Mail adresi
            'extra2' => 'Site Yöneticisi', // Gönderen adı
        ]);

        \App\Models\SiteMovement::create([
            'id' => 3,
            'name' => 'Toplu Mail Gönderimi',
            'extra1' => '<EMAIL>', // Mail adresi
            'extra2' => 'Site Yöneticisi', // Gönderen adı
        ]);
    }
}
