<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MemberBenefitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Test kullanıcısı (ID: 1) için örnek yan hak seçimleri
        \App\Models\MemberBenefit::create([
            'benefit_id' => 123, // Özel Sağlık Sigortası
            'amount' => 0.00,
            'member_id' => 1, // Test kullanıcısı
            'update_status' => 1,
            'benefit_option_id' => 216, // PLAN 1 + Diş&Göz
        ]);

        \App\Models\MemberBenefit::create([
            'benefit_id' => 36, // Hayat & Ferdi Kaza Sigortası
            'amount' => 500.00, // Esnek tutar
            'member_id' => 1,
            'update_status' => 1,
            'benefit_option_id' => 0, // Seçenek yok, esnek tutar
        ]);

        \App\Models\MemberBenefit::create([
            'benefit_id' => 153, // Migros Money Club Pro Alışveriş Kartı
            'amount' => 1000.00, // Esnek tutar
            'member_id' => 1,
            'update_status' => 1,
            'benefit_option_id' => 0, // Seçenek yok, esnek tutar
        ]);

        // Test kullanıcısı 2 (ID: 2) için de örnek seçimler
        \App\Models\MemberBenefit::create([
            'benefit_id' => 123, // Özel Sağlık Sigortası
            'amount' => 0.00,
            'member_id' => 2,
            'update_status' => 1,
            'benefit_option_id' => 214, // PLAN-1 (Grup Sağlık Sigortası)
        ]);
    }
}
