<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BenefitOptionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Özel Sağlık Sigortası seçenekleri (benefit_id: 123)
        \App\Models\BenefitOption::create([
            'id' => 214,
            'name' => 'PLAN-1 (Grup Sağlık Sigortası)',
            'description' => '',
            'alert_message' => 'Lütfen mevcut planınız ile geçiş yaptığınız planın teminatlarını karşılaştırınız ve bu doğrultuda ihtiyaçlarınıza göre seçim yapınız.',
            'image' => '14-12/11/sosyal_sorumluluk-1418311618.png',
            'comment' => '',
            'alert_color' => '',
            'price' => 4750.00,
            'status' => 1, // Aktif seçenek
            'benefit_id' => 123,
            'update_status' => 1,
            'order_no' => 0,
            'is_addon' => 0,
            'price_original' => 0.00,
            'has_details' => 1,
            'details' => '<p>PLAN-1 detayları burada yer alacak...</p>',
        ]);

        \App\Models\BenefitOption::create([
            'id' => 215,
            'name' => 'PLAN 1 + Doğum',
            'description' => '',
            'alert_message' => 'Lütfen mevcut planınız ile geçiş yaptığınız planın teminatlarını karşılaştırınız ve bu doğrultuda ihtiyaçlarınıza göre seçim yapınız.',
            'image' => '14-12/11/sosyal_sorumluluk-1418311626.png',
            'comment' => '',
            'alert_color' => '',
            'price' => 4800.00,
            'status' => 1,
            'benefit_id' => 123,
            'update_status' => 1,
            'order_no' => 0,
            'is_addon' => 0,
            'price_original' => 0.00,
            'has_details' => 1,
            'details' => '<p>PLAN 1 + Doğum detayları burada yer alacak...</p>',
        ]);

        \App\Models\BenefitOption::create([
            'id' => 216,
            'name' => 'PLAN 1 + Diş&Göz',
            'description' => '',
            'alert_message' => 'Lütfen mevcut planınız ile geçiş yaptığınız planın teminatlarını karşılaştırınız ve bu doğrultuda ihtiyaçlarınıza göre seçim yapınız.',
            'image' => '14-12/11/sosyal_sorumluluk-1418311641.png',
            'comment' => '',
            'alert_color' => '',
            'price' => 5000.00,
            'status' => 1,
            'benefit_id' => 123,
            'update_status' => 1,
            'order_no' => 0,
            'is_addon' => 0,
            'price_original' => 0.00,
            'has_details' => 1,
            'details' => '<p>PLAN 1 + Diş&Göz detayları burada yer alacak...</p>',
        ]);
    }
}
