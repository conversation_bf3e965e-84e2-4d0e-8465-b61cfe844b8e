<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class StaticPageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        \App\Models\StaticPage::create([
            'name' => 'Giriş Sayfası Bilgilendirme',
            'title' => 'Hoş Geldiniz',
            'content' => '<p>Esnek Yan Haklar sistemine hoş geldiniz. Bu sistem ile yan haklarınızı esnek şekilde kullanabilirsiniz.</p>',
            'name_en' => 'Login Page Information',
            'title_en' => 'Welcome',
            'content_en' => '<p>Welcome to the Flexible Benefits system. You can use your benefits flexibly with this system.</p>',
            'seo' => 'giris-sayfasi-bilgilendirme',
            'before_login' => true,
            'is_visible' => true,
            'item_id' => 0,
        ]);

        \App\Models\StaticPage::create([
            'name' => '<PERSON>llanım Kılavuzu',
            'title' => 'Nasıl Kullanılır?',
            'content' => '<p>Bu sayfada sistemin nasıl kullanılacağına dair bilgiler yer almaktadır.</p>',
            'name_en' => 'User Guide',
            'title_en' => 'How to Use?',
            'content_en' => '<p>This page contains information on how to use the system.</p>',
            'seo' => 'kullanim-kilavuzu',
            'before_login' => true,
            'is_visible' => true,
            'item_id' => 0,
        ]);
    }
}
