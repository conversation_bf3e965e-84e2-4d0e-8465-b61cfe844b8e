<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MemberFamilySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Test kullanıcısı (ID: 1) için örnek aile üyeleri
        \App\Models\MemberFamily::create([
            'name' => 'Ayşe Test',
            'identity_number' => '12345678901',
            'member_id' => 1,
            'update_status' => 1,
            'order_no' => 0,
            'type' => 'spouse', // eş
            'birth_date' => '1990-05-15',
        ]);

        \App\Models\MemberFamily::create([
            'name' => 'Ali Test',
            'identity_number' => '12345678902',
            'member_id' => 1,
            'update_status' => 1,
            'order_no' => 1,
            'type' => 'child', // çocuk
            'birth_date' => '2015-08-20',
        ]);

        \App\Models\MemberFamily::create([
            'name' => 'Fatma Test',
            'identity_number' => '12345678903',
            'member_id' => 1,
            'update_status' => 1,
            'order_no' => 2,
            'type' => 'child', // çocuk
            'birth_date' => '2018-03-10',
        ]);

        // Test kullanıcısı 2 (ID: 2) için de örnek aile üyesi
        \App\Models\MemberFamily::create([
            'name' => 'Mehmet Test',
            'identity_number' => '12345678904',
            'member_id' => 2,
            'update_status' => 1,
            'order_no' => 0,
            'type' => 'child',
            'birth_date' => '2020-01-05',
        ]);
    }
}
