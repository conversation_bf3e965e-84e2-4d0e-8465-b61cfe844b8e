<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BenefitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        \App\Models\Benefit::create([
            'id' => 36,
            'order_no' => 15.0,
            'name' => 'Hayat & Ferdi Kaza Sigortası',
            'benefit_group_id' => 1,
            'description' => '<p>Şirketimizin, çalışanlarının yaşam güvencesini sağlamak ve onların yaşamları boyunca karşılaşabilecekleri risklere karşı önlem almak amacıyla yaptırdığı sigortadır.</p>',
            'alert_message' => 'Yatırılan tutar işveren katkısı olup %25 devlet katkısı hak edişi olmayacaktır.',
            'image' => '19-04/24/300x300_0001_hayat-ve-ferdi-kaza-gorsel-png-1-1556117145.jpg',
            'alert_color' => 'red',
            'price' => 0.00,
            'status' => 2,
            'has_choice' => 0,
            'has_flexible_price' => 1,
            'flexible_price' => 'TL',
            'type' => '1',
            'flexible_amount_title' => 'İlave Aktarmak İstediğiniz Katkı Payı Tutarı:',
            'title' => 'Hayat & Ferdi Kaza Sigortası',
            'hide_if_not_predefined' => 1,
            'show_on_positive_balance' => 0,
            'price_original' => 0.00,
        ]);

        \App\Models\Benefit::create([
            'id' => 123,
            'order_no' => 11.0,
            'name' => 'Özel Sağlık Sigortası',
            'benefit_group_id' => 11,
            'description' => '',
            'alert_message' => '01.03.2019 tarihinde yenilenen grup sağlık personel poliçesi kapsamında bugüne kadar yaptığınız sağlık harcamaları, seçeceğiniz sağlık sigortası limitleri kapsamına yansıtılmayacak olup 01.06.2019 tarihi itibariyle yeni limitler ile poliçe başlatılacaktır.',
            'image' => '19-04/24/300x300_0000_saglik-sigortasi_gorsel_png_648928.jpg',
            'alert_color' => 'red',
            'price' => 0.00,
            'status' => 2,
            'has_choice' => 1,
            'has_flexible_price' => 0,
            'type' => '4',
            'title' => 'Özel Sağlık Sigortası',
            'hide_if_not_predefined' => 1,
            'show_on_positive_balance' => 0,
            'price_original' => 0.00,
        ]);

        \App\Models\Benefit::create([
            'id' => 153,
            'order_no' => 16.0,
            'name' => 'Migros Money Club Pro Alışveriş Kartı',
            'benefit_group_id' => 15,
            'description' => '<p>Limitini bütçen dahilinde dilediğin gibi belirleyebileceğin Migros Money Club Pro Kartı tüm Türkiye\'deki Migros, 5M Migros, MakroCenter ve online sipariş aracı Migros Sanal Market\'te kullanabilirsin.</p>',
            'alert_message' => '*Virgülden sonra 2 haneye kadar işlem yapılabilir.',
            'image' => '19-05/03/migros-logo.png',
            'alert_color' => 'red',
            'price' => 0.00,
            'status' => 2,
            'has_choice' => 0,
            'has_flexible_price' => 1,
            'flexible_price' => 'TL',
            'type' => '3',
            'title' => 'Migros Money Club Pro Alışveriş Kartı',
            'hide_if_not_predefined' => 0,
            'show_on_positive_balance' => 0,
            'price_original' => 0.00,
            'convert_price' => 1,
        ]);
    }
}
