<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MemberBenefitCartSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Test kullanıcısı için örnek sepet verisi (isteğe bağlı)
        // Şimdilik boş bırakıyoruz, gerekirse daha sonra eklenebilir

        // \App\Models\MemberBenefitCart::create([
        //     'benefit_id' => 153, // Migros Money Club Pro
        //     'amount' => 500.00,
        //     'member_id' => 1,
        //     'update_status' => 1,
        //     'benefit_option_id' => 0,
        //     'member_family_id' => 0,
        //     'is_removed' => 0,
        // ]);
    }
}
