<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MemberSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        \App\Models\Member::create([
            'identity_number' => '<EMAIL>',
            'password' => md5('123456'), // Basit şifre hash'i
            'email' => '<EMAIL>',
            'name' => 'Dilan Test User',
            'status' => 1, // Aktif kullanıcı
            'item_id' => 0,
            'percentage' => 100,
            'positive_balance' => 1000,
        ]);

        \App\Models\Member::create([
            'identity_number' => '<EMAIL>',
            'password' => md5('password'),
            'email' => '<EMAIL>',
            'name' => 'Test User',
            'status' => 1, // Aktif kullanıcı
            'item_id' => 0,
            'percentage' => 100,
            'positive_balance' => 1000,
        ]);
    }
}
