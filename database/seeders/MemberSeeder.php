<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class MemberSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        \App\Models\Member::create([
            'identity_number' => '<EMAIL>',
            'password' => \Illuminate\Support\Facades\Hash::make('123456'), // Laravel Hash
            'email' => '<EMAIL>',
            'name' => 'Dilan Test User',
            'status' => 1, // Aktif kullanıcı (0 = pasif, 1 = aktif)
            'item_id' => 0,
            'percentage' => 100,
            'positive_balance' => 1000,
        ]);

        \App\Models\Member::create([
            'identity_number' => '<EMAIL>',
            'password' => \Illuminate\Support\Facades\Hash::make('password'), // Laravel Hash
            'email' => '<EMAIL>',
            'name' => 'Test User',
            'status' => 1, // Aktif kullan<PERSON> (0 = pasif, 1 = aktif)
            'item_id' => 0,
            'percentage' => 100,
            'positive_balance' => 1000,
        ]);
    }
}
